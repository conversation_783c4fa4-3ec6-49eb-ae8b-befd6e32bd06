JOB_DESCRIPTION = """
# Junior React Developer

**Position:** Junior React Developer  
**Duration:** 12-month contract with the possibility of extension based on performance and project needs.

We are seeking a motivated Junior React Developer to join our team and assist in the development of our cutting-edge Next.js web application. This project integrates the Vercel AI SDK to enhance user experience with advanced AI-driven features. 

**Key Responsibilities:**
- Develop and maintain React components and Next.js applications.
- Integrate AI-driven features using the Vercel AI SDK.
- Collaborate with senior developers to design and implement new features.
- Optimize application performance and ensure responsiveness across different devices.
- Participate in code reviews and contribute to best practices.
- Troubleshoot and debug issues to ensure the highest quality of the web application.

**Qualifications:**
- 1-2 years of experience in front-end development with React and Next.js.
- Proficiency in JavaScript, TypeScript, CSS, and HTML.
- Experience with Git and RESTful APIs.
- Familiarity with Vercel AI SDK is a plus.
- Strong problem-solving skills and attention to detail.
- Excellent communication and teamwork abilities.
- Ability to work independently and take initiative on projects.

**What We Offer:**
- Opportunity to work with cutting-edge technologies and AI integration.
- Collaborative and supportive work environment.
- Mentorship from senior developers to help grow your skills.
- Potential for role extension and career advancement within the company.
- Flexible working hours and the possibility of remote work.

This role is ideal for someone looking to grow their skills in Next.js, React, and AI-powered web applications while contributing to impactful projects.
"""

SKILLS = [
    "React",
    "Next.js",
    "JavaScript",
    "TypeScript",
    "Vercel AI SDK",
    "CSS",
    "HTML",
    "Git",
    "REST APIs",
    "CrewAI",
]
