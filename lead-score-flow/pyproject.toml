[project]
name = "lead_score_flow"
version = "0.1.0"
description = "lead_score_flow using crewAI"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.10,<=3.13"
dependencies = [
    "crewai[tools]==0.130.0",
    "langchain-tools>=0.1.34",
    "crewai-tools>=0.12.0",
    "google-auth-oauthlib>=1.2.1",
    "google-api-python-client>=2.145.0",
    "pyvis>=0.3.2",
    "asyncio>=3.4.3",
]

[project.scripts]
kickoff = "lead_score_flow.main:kickoff"
plot = "lead_score_flow.main:plot"

[build-system]
requires = [
    "hatchling",
]
build-backend = "hatchling.build"
