[project]
name = "write_a_book_with_flows"
version = "0.1.0"
description = "write_a_book_with_flows using crewAI"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.10,<=3.13"
dependencies = [
    "asyncio>=3.4.3",
    "crewai[tools]==0.130.0",
]

[project.scripts]
kickoff = "write_a_book_with_flows.main:kickoff"
plot = "write_a_book_with_flows.main:plot"

[build-system]
requires = [
    "hatchling",
]
build-backend = "hatchling.build"

[tool.crewai]
type = "flow"