[tool.poetry]
name = "marketing_posts"
version = "0.1.0"
description = "marketing-posts using crewAI"
authors = ["raspawar <EMAIL>"]

[tool.poetry.dependencies]
python = ">=3.10,<=3.13"
langchain-nvidia-ai-endpoints = "^0.3.5"
python-dotenv = "^1.0.1"
crewai = "^0.130.0"
crewai-tools = "^0.14.0"

[tool.poetry.scripts]
marketing_posts = "marketing_posts.main:run"
train = "marketing_posts.main:train"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
