{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Install dependencies"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U --quiet langchain-community tiktoken langchain-openai langchainhub chromadb langchain langgraph langchain-text-splitters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Set environment variables"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["import getpass\n", "import time\n", "initial_time = time.time()\n", "\n", "import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your OpenAI API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup Retriever"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=100, chunk_overlap=50\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Retriever Tool"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langchain.tools.retriever import create_retriever_tool\n", "\n", "retriever_tool = create_retriever_tool(\n", "    retriever,\n", "    \"retrieve_blog_posts\",\n", "    \"Search and return information about <PERSON><PERSON> blog posts on LLM agents, prompt engineering, and adversarial attacks on LLMs.\",\n", ")\n", "\n", "tools = [retriever_tool]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Agent State"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from typing import Annotated, Sequence\n", "from typing_extensions import TypedDict\n", "\n", "from langchain_core.messages import BaseMessage\n", "\n", "from langgraph.graph.message import add_messages\n", "\n", "\n", "class AgentState(TypedDict):\n", "    # The add_messages function defines how an update should be processed\n", "    # Default is to replace. add_messages says \"append\"\n", "    messages: Annotated[Sequence[BaseMessage], add_messages]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Nodes and Edges"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["********************Prompt[rlm/rag-prompt]********************\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.11.7/lib/python3.11/site-packages/langsmith/client.py:241: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\n", "Question: \u001b[33;1m\u001b[1;3m{question}\u001b[0m \n", "Context: \u001b[33;1m\u001b[1;3m{context}\u001b[0m \n", "Answer:\n"]}], "source": ["from typing import Annotated, Literal, Sequence\n", "from typing_extensions import TypedDict\n", "\n", "from langchain import hub\n", "from langchain_core.messages import BaseMessage, HumanMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "from langgraph.prebuilt import tools_condition\n", "\n", "### Edges\n", "\n", "\n", "def grade_documents(state) -> Literal[\"generate\", \"rewrite\"]:\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (messages): The current state\n", "\n", "    Returns:\n", "        str: A decision for whether the documents are relevant or not\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> RELEVANCE---\")\n", "\n", "    # Data model\n", "    class grade(BaseModel):\n", "        \"\"\"Binary score for relevance check.\"\"\"\n", "\n", "        binary_score: str = Field(description=\"Relevance score 'yes' or 'no'\")\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4o-mini\", streaming=True)\n", "\n", "    # LLM with tool and validation\n", "    llm_with_tool = model.with_structured_output(grade)\n", "\n", "    # Prompt\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n\n", "        Here is the retrieved document: \\n\\n {context} \\n\\n\n", "        Here is the user question: {question} \\n\n", "        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "        Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\",\n", "        input_variables=[\"context\", \"question\"],\n", "    )\n", "\n", "    # Chain\n", "    chain = prompt | llm_with_tool\n", "\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "\n", "    question = messages[0].content\n", "    docs = last_message.content\n", "\n", "    scored_result = chain.invoke({\"question\": question, \"context\": docs})\n", "\n", "    score = scored_result.binary_score\n", "\n", "    if score == \"yes\":\n", "        print(\"---DECISION: DOCS RELEVANT---\")\n", "        return \"generate\"\n", "\n", "    else:\n", "        print(\"---DECISION: DOCS NOT RELEVANT---\")\n", "        print(score)\n", "        return \"rewrite\"\n", "\n", "\n", "### Nodes\n", "\n", "\n", "def agent(state):\n", "    \"\"\"\n", "    Invokes the agent model to generate a response based on the current state. Given\n", "    the question, it will decide to retrieve using the retriever tool, or simply end.\n", "\n", "    Args:\n", "        state (messages): The current state\n", "\n", "    Returns:\n", "        dict: The updated state with the agent response appended to messages\n", "    \"\"\"\n", "    print(\"---CALL AGENT---\")\n", "    messages = state[\"messages\"]\n", "    model = ChatOpenAI(temperature=0, streaming=True, model=\"gpt-4-turbo\")\n", "    model = model.bind_tools(tools)\n", "    response = model.invoke(messages)\n", "    # We return a list, because this will get added to the existing list\n", "    return {\"messages\": [response]}\n", "\n", "\n", "def rewrite(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (messages): The current state\n", "\n", "    Returns:\n", "        dict: The updated state with re-phrased question\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    messages = state[\"messages\"]\n", "    question = messages[0].content\n", "\n", "    msg = [\n", "        HumanMessage(\n", "            content=f\"\"\" \\n\n", "    Look at the input and try to reason about the underlying semantic intent / meaning. \\n\n", "    Here is the initial question:\n", "    \\n ------- \\n\n", "    {question}\n", "    \\n ------- \\n\n", "    Formulate an improved question: \"\"\",\n", "        )\n", "    ]\n", "\n", "    # Grader\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4o-mini\", streaming=True)\n", "    response = model.invoke(msg)\n", "    return {\"messages\": [response]}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (messages): The current state\n", "\n", "    Returns:\n", "         dict: The updated state with re-phrased question\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    messages = state[\"messages\"]\n", "    question = messages[0].content\n", "    last_message = messages[-1]\n", "\n", "    docs = last_message.content\n", "\n", "    # Prompt\n", "    prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "    # LLM\n", "    llm = ChatOpenAI(model_name=\"gpt-4o-mini\", temperature=0, streaming=True)\n", "\n", "    # Post-processing\n", "    def format_docs(docs):\n", "        return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "    # Chain\n", "    rag_chain = prompt | llm | StrOutputParser()\n", "\n", "    # Run\n", "    response = rag_chain.invoke({\"context\": docs, \"question\": question})\n", "    return {\"messages\": [response]}\n", "\n", "\n", "print(\"*\" * 20 + \"Prompt[rlm/rag-prompt]\" + \"*\" * 20)\n", "prompt = hub.pull(\"rlm/rag-prompt\").pretty_print()  # Show what the prompt looks like"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Graph"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "from langgraph.prebuilt import ToolNode\n", "\n", "# Define a new graph\n", "workflow = StateGraph(AgentState)\n", "\n", "# Define the nodes we will cycle between\n", "workflow.add_node(\"agent\", agent)  # agent\n", "retrieve = ToolNode([retriever_tool])\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieval\n", "workflow.add_node(\"rewrite\", rewrite)  # Re-writing the question\n", "workflow.add_node(\n", "    \"generate\", generate\n", ")  # Generating a response after we know the documents are relevant\n", "# Call agent node to decide to retrieve or not\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# Decide whether to retrieve\n", "workflow.add_conditional_edges(\n", "    \"agent\",\n", "    # Assess agent decision\n", "    tools_condition,\n", "    {\n", "        # Translate the condition outputs to nodes in our graph\n", "        \"tools\": \"retrieve\",\n", "        END: END,\n", "    },\n", ")\n", "\n", "# Edges taken after the `action` node is called.\n", "workflow.add_conditional_edges(\n", "    \"retrieve\",\n", "    # Assess agent decision\n", "    grade_documents,\n", ")\n", "workflow.add_edge(\"generate\", END)\n", "workflow.add_edge(\"rewrite\", \"agent\")\n", "\n", "# Compile\n", "graph = workflow.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Plot Graph"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph(xray=True).draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run Graph"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---CALL AGENT---\n", "\"Output from node 'agent':\"\n", "'---'\n", "{ 'messages': [ AIMessage(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'call_R8JiVuIfmbTxeMNldY63YMgi', 'function': {'arguments': '{\"query\":\"types of agent memory\"}', 'name': 'retrieve_blog_posts'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4-turbo-2024-04-09', 'system_fingerprint': 'fp_1e9a2d1e89'}, id='run-d81a8fad-7d66-4690-bc8c-9cf2a8dc02ec-0', tool_calls=[{'name': 'retrieve_blog_posts', 'args': {'query': 'types of agent memory'}, 'id': 'call_R8JiVuIfmbTxeMNldY63YMgi', 'type': 'tool_call'}])]}\n", "'\\n---\\n'\n", "---CHECK RELEVANCE---\n", "---DECISION: DOCS NOT RELEVANT---\n", "no\n", "\"Output from node 'retrieve':\"\n", "'---'\n", "{ 'messages': [ ToolMessage(content='Table of Contents\\n\\n\\n\\nAgent System Overview\\n\\nComponent One: Planning\\n\\nTask Decomposition\\n\\nSelf-Reflection\\n\\n\\nComponent Two: Memory\\n\\nTypes of Memory\\n\\nMaximum Inner Product Search (MIPS)\\n\\n\\nComponent Three: Tool Use\\n\\nCase Studies\\n\\nScientific Discovery Agent\\n\\nGenerative Agents Simulation\\n\\nProof-of-Concept Examples\\n\\n\\nChallenges\\n\\nCitation\\n\\nReferences\\n\\nTable of Contents\\n\\n\\n\\nAgent System Overview\\n\\nComponent One: Planning\\n\\nTask Decomposition\\n\\nSelf-Reflection\\n\\n\\nComponent Two: Memory\\n\\nTypes of Memory\\n\\nMaximum Inner Product Search (MIPS)\\n\\n\\nComponent Three: Tool Use\\n\\nCase Studies\\n\\nScientific Discovery Agent\\n\\nGenerative Agents Simulation\\n\\nProof-of-Concept Examples\\n\\n\\nChallenges\\n\\nCitation\\n\\nReferences\\n\\nTable of Contents\\n\\n\\n\\nAgent System Overview\\n\\nComponent One: Planning\\n\\nTask Decomposition\\n\\nSelf-Reflection\\n\\n\\nComponent Two: Memory\\n\\nTypes of Memory\\n\\nMaximum Inner Product Search (MIPS)\\n\\n\\nComponent Three: Tool Use\\n\\nCase Studies\\n\\nScientific Discovery Agent\\n\\nGenerative Agents Simulation\\n\\nProof-of-Concept Examples\\n\\n\\nChallenges\\n\\nCitation\\n\\nReferences\\n\\nPlanning\\n\\nSubgoal and decomposition: The agent breaks down large tasks into smaller, manageable subgoals, enabling efficient handling of complex tasks.\\nReflection and refinement: The agent can do self-criticism and self-reflection over past actions, learn from mistakes and refine them for future steps, thereby improving the quality of final results.\\n\\n\\nMemory', name='retrieve_blog_posts', id='936c1034-d281-46d5-9225-be750a61b60c', tool_call_id='call_R8JiVuIfmbTxeMNldY63YMgi')]}\n", "'\\n---\\n'\n", "---TRANSFORM QUERY---\n", "\"Output from node 'rewrite':\"\n", "'---'\n", "{ 'messages': [ AIMessage(content='What insights does <PERSON><PERSON> provide regarding the different types of memory in agents?', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_6fc10e10eb'}, id='run-fdb07ad3-6d05-44c3-b6f8-e6a338614f41-0')]}\n", "'\\n---\\n'\n", "---CALL AGENT---\n", "\"Output from node 'agent':\"\n", "'---'\n", "{ 'messages': [ AIMessage(content='<PERSON><PERSON> discusses the concept of memory in agents under the \"Component Two: Memory\" section of her blog. She categorizes memory into different types, although the specific details or categories of these types are not explicitly listed in the brief content overview provided. However, she does mention the use of techniques like Maximum Inner Product Search (MIPS) which is often related to memory systems in agents, suggesting a focus on efficient retrieval mechanisms which could be part of how different types of memory are utilized or implemented in agent systems. This indicates a technical approach to understanding and optimizing how memory functions within agents, possibly differentiating between types based on functionality, such as short-term, long-term, or task-specific memories.', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4-turbo-2024-04-09', 'system_fingerprint': 'fp_1e9a2d1e89'}, id='run-5a1f3733-e1ca-4a14-840a-b65c672be530-0')]}\n", "'\\n---\\n'\n"]}], "source": ["import pprint\n", "\n", "inputs = {\n", "    \"messages\": [\n", "        (\"user\", \"What does <PERSON><PERSON> say about the types of agent memory?\"),\n", "    ]\n", "}\n", "for output in graph.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint.pprint(f\"Output from node '{key}':\")\n", "        pprint.pprint(\"---\")\n", "        pprint.pprint(value, indent=2, width=80, depth=None)\n", "    pprint.pprint(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total execution time: 914.08 seconds\n"]}], "source": ["import time\n", "final_time = time.time()\n", "print(f\"Total execution time: {final_time - initial_time:.2f} seconds\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}