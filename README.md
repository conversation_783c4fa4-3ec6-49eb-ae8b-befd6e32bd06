# Examples for crewAI
## Introduction
crewAI is designed to facilitate the collaboration of role-playing AI agents.
This is a collection of examples of different ways to use the crewAI framework to automate the processes.
By [@joaomdmoura](https://x.com/joaomdmoura).

**Note**: All examples have been standardized to use **CrewAI version 0.130.0** for consistency and compatibility.

## Examples
- [Marketing Strategy](https://github.com/joaomdmoura/crewAI-examples/tree/main/marketing_strategy)
- [Surprise Trip](https://github.com/joaomdmoura/crewAI-examples/tree/main/surprise_trip)
- [Match to Proposal](https://github.com/joaomdmoura/crewAI-examples/tree/main/match_profile_to_positions)
- [Find Job Candidades Demo](https://github.com/joaomdmoura/crewAI-examples/tree/main/recruitment)
- [Create Job Posting](https://github.com/joaomdmoura/crewAI-examples/tree/main/job-posting)
- [Game Generator](https://github.com/joaomdmoura/crewAI-examples/tree/main/game-builder-crew)

## Old Examples, need to be updated

### Basic Examples

- [Trip Planner](https://github.com/joaomdmoura/crewAI-examples/tree/main/trip_planner)
- [Create Instagram Post](https://github.com/joaomdmoura/crewAI-examples/tree/main/instagram_post)
- [Markdown Validator](https://github.com/joaomdmoura/crewAI-examples/tree/main/markdown_validator)
- [Using Azure OpenAI API](https://github.com/joaomdmoura/crewAI-examples/tree/main/azure_model)

Starting your own example
  - [Starter Template](https://github.com/joaomdmoura/crewAI-examples/tree/main//starter_template)
### Advanced Examples
- [Stock Analysis](https://github.com/joaomdmoura/crewAI-examples/tree/main/stock_analysis)
- [Landing Page Generator](https://github.com/joaomdmoura/crewAI-examples/tree/main/landing_page_generator)
- [CrewAI + LangGraph](https://github.com/joaomdmoura/crewAI-examples/tree/main/CrewAI-LangGraph)