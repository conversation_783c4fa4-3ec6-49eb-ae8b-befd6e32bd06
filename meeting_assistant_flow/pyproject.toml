[project]
name = "meeting_assistant_flow"
version = "0.1.0"
description = "meeting_assistant_flow using crewAI"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.10,<=3.13"
dependencies = [
    "crewai[tools]==0.130.0",
    "asyncio>=3.4.3",
    "slack-sdk>=3.33.1",
]

[project.scripts]
kickoff = "meeting_assistant_flow.main:kickoff"
plot = "meeting_assistant_flow.main:plot"

[build-system]
requires = [
    "hatchling",
]
build-backend = "hatchling.build"
