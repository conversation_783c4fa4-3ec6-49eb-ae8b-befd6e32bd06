syntax_review_task:
  description: >
    Use the markdown_validation_tool to review the file(s) at this path: {filename}.
    Be sure to pass only the file path to the markdown_validation_tool.
    Use the following format to call the markdown_validation_tool:
    Do I need to use a tool? Yes
    Action: markdown_validation_tool
    Action Input: {filename}

    Get the validation results from the tool and then summarize it into a list of changes
    the developer should make to the document.
    DO NOT recommend ways to update the document.
    DO NOT change any of the content of the document or add content to it. 
    It is critical to your task to only respond with a list of changes.

    If you already know the answer or if you do not need to use a tool, 
    return it as your Final Answer.
  expected_output: >
    A list of changes the developer should make to the document based on the markdown validation results.
