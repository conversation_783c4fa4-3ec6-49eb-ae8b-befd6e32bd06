[tool.poetry]
name = "recruitment"
version = "0.1.0"
description = "recruitment using crewAI"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10,<=3.13"
crewai = { extras = ["tools"], version = "^0.130.0" }
selenium = "^4.21.0"

[tool.poetry.scripts]
recruitment = "recruitment.main:run"
train = "recruitment.main:train"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
