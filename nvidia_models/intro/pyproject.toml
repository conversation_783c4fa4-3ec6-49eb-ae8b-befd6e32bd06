[tool.poetry]
name = "nvidia-intro-crewai-example"
version = "0.1.0"
description = ""
authors = ["raspawar <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10.0,<3.12"
python-dotenv = "1.0.0"
litellm = "^1.52.10"
langchain-nvidia-ai-endpoints = "^0.3.5"
crewai = "^0.130.0"

[tool.pyright]
# https://github.com/microsoft/pyright/blob/main/docs/configuration.md
useLibraryCodeForTypes = true
exclude = [".cache"]

[tool.ruff.lint]
select = [
    "E",     # pycodestyle
    "F",     # pyflakes
    "I",     # isort
    "B",     # flake8-bugbear
    "C4",    # flake8-comprehensions
    "ARG",   # flake8-unused-arguments
    "SIM",   # flake8-simplify
    "T201",  # print
]
ignore = [
    "W291",  # trailing whitespace
    "W292",  # no newline at end of file
    "W293",  # blank line contains whitespace
]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"