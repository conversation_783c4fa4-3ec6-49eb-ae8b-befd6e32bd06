[project]
name = "email_auto_responder_flow"
version = "0.1.0"
description = "email_auto_responder_flow using crewAI"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
requires-python = ">=3.10,<=3.13"
dependencies = [
    "crewai[tools]==0.130.0",
    "asyncio",
    "langchain-tools>=0.1.34",
    "crewai-tools>=0.12.0",
    "google-auth-oauthlib>=1.2.1",
    "google-api-python-client>=2.145.0",
]

[project.scripts]
kickoff = "email_auto_responder_flow.main:kickoff"
plot = "email_auto_responder_flow.main:plot"

[build-system]
requires = [
    "hatchling",
]
build-backend = "hatchling.build"
