[tool.poetry]
name = "match_to_proposal"
version = "0.1.0"
description = "match_to_proposal using crewAI"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10,<=3.13"
crewai = { extras = ["tools"], version = "^0.130.0" }
crewai-tools = "^0.4.6"

[tool.poetry.scripts]
match_to_proposal = "match_to_proposal.main:run"
train = "match_to_proposal.main:train"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
