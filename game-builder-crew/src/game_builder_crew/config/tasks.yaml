code_task:
  description: >
    You will create a game using python, these are the instructions:

    Instructions
    ------------
    {game}
    
  expected_output: >
    Your Final answer must be the full python code, only the python code and nothing else.

review_task:
  description: >
    You will create a game using python, these are the instructions:

    Instructions
    ------------
    {game}

    Using the code you got, check for errors. Check for logic errors,
    syntax errors, missing imports, variable declarations, mismatched brackets,
    and security vulnerabilities.
  expected_output: >
    Your Final answer must be the full python code, only the python code and nothing else.

evaluate_task:
  description: >
    You are helping create a game using python, these are the instructions:

    Instructions
    ------------
    {game}

    You will look over the code to insure that it is complete and
    does the job that it is supposed to do.
  expected_output: >
    Your Final answer must be the full python code, only the python code and nothing else.