# AI Crew using Azure OpenAI Endpoint

## Introduction
This is a simple example using the CrewAI framework with an Azure Open AI endpoint.

## Running the Script
This example uses the Azure OpenAI API to call a model. 

- **Configure Environment**: Copy ``.env.example` and set up the environment variables the model, endpoint url, and api key.
- **Install Dependencies**: Run `poetry install --no-root` (uses crewAI==0.130.0).
- **Execute the Script**: Run `python main.py` to see a list of recommended changes to this document.

## Details & Explanation
- **Running the Script**: Execute `python main.py`. The script will leverage the CrewAI framework to process the specified file and return a list of changes.

## License
This project is released under the MIT License.