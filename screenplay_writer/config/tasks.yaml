task0:
  description: >
    Read the following newsgroup post. If this contains vulgar language reply with STOP . If this is spam reply with STOP.
    ### NEWGROUP POST:
    {{discussion}}
  expected_output: >
    Either "STOP" if the post contains vulgar language or is spam, or no response if it does not.

task1:
  description: >
    Analyse in much detail the following discussion:
    ### DISCUSSION:
    {{discussion}}
  expected_output: >
    A detailed analysis of the discussion, identifying who said what and rewording if necessary while maintaining the main discussion points.

task2:
  description: >
    Create a dialogue heavy screenplay from the discussion, between two persons. Do NOT write parentheticals. Leave out wrylies. You MUST SKIP directional notes.
  expected_output: >
    A screenplay dialogue consisting only of the dialogue parts between two persons, without parentheticals, wrylies, or directional notes.

task3:
  description: >
    Format the script exactly like this:
      ## (person 1):
      (first text line from person 1)
             
      ## (person 2):
      (first text line from person 2)
             
      ## (person 1):
      (second text line from person 1)
             
      ## (person 2):
      (second text line from person 2)
  expected_output: >
    A formatted script with the specified structure, ensuring each line is formatted according to the provided template.

task4:
  description: >
    Score the following script:
    ### SCRIPT:
    {{script}}
  expected_output: >
    A score from 1 to 10, indicating how well the script is.
